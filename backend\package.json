{"name": "edublock-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "debug": "node --inspect server.js"}, "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.12.0", "sequelize": "^6.37.5", "ws": "^8.18.1"}, "devDependencies": {"nodemon": "^3.0.2", "sequelize-cli": "^6.6.2"}}