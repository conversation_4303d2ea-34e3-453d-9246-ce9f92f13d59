const Joi = require('joi');

const validateRegistration = (req, res, next) => {
    const schema = Joi.object({
        firstName: Joi.string()
            .required()
            .min(2)
            .max(50)
            .messages({
                'string.empty': 'Le prénom est requis',
                'string.min': 'Le prénom doit contenir au moins 2 caractères',
                'string.max': 'Le prénom ne doit pas dépasser 50 caractères'
            }),
        lastName: Joi.string()
            .required()
            .min(2)
            .max(50)
            .messages({
                'string.empty': 'Le nom est requis',
                'string.min': 'Le nom doit contenir au moins 2 caractères',
                'string.max': 'Le nom ne doit pas dépasser 50 caractères'
            }),
        email: Joi.string()
            .email()
            .required()
            .messages({
                'string.email': 'Format d\'email invalide',
                'string.empty': 'L\'email est requis'
            }),
        password: Joi.string()
            .required()
            .min(8)
            .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
            .messages({
                'string.empty': 'Le mot de passe est requis',
                'string.min': 'Le mot de passe doit contenir au moins 8 caractères',
                'string.pattern.base': 'Le mot de passe doit contenir au moins une majuscule, une minuscule, un chiffre et un caractère spécial'
            }),
        role: Joi.string()
            .required()
            .valid('STUDENT', 'TEACHER')
            .messages({
                'any.only': 'Le rôle doit être soit STUDENT soit TEACHER'
            }),
        specialization: Joi.when('role', {
            is: 'TEACHER',
            then: Joi.string().required().min(2).max(100),
            otherwise: Joi.string().optional()
        }),
        studentId: Joi.when('role', {
            is: 'STUDENT',
            then: Joi.string().required().pattern(/^\d{8}$/),
            otherwise: Joi.string().optional()
        }),
        biometricData: Joi.string().required()
    });

    const { error } = schema.validate(req.body);
    if (error) {
        return res.status(400).json({
            error: 'Validation failed',
            message: error.details[0].message
        });
    }
    next();
};

const validateLogin = (req, res, next) => {
    const schema = Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().required()
    });

    const { error } = schema.validate(req.body);
    if (error) {
        return res.status(400).json({
            error: 'Validation failed',
            message: error.details[0].message
        });
    }
    next();
};

module.exports = {
    validateRegistration,
    validateLogin
};
