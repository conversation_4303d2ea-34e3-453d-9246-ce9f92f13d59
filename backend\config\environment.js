require('dotenv').config();

module.exports = {
    port: process.env.PORT || 3002,
    nodeEnv: process.env.NODE_ENV || 'development',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    database: {
        name: process.env.DB_NAME || 'edublock',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306
    },
    redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        ttl: parseInt(process.env.REDIS_TTL) || 300
    },
    email: {
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT) || 587,
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
        from: process.env.EMAIL_FROM
    },
    blockchain: {
        type: process.env.BLOCKCHAIN_TYPE || 'HYPERLEDGER_FABRIC',
        networkUrl: process.env.BLOCKCHAIN_NETWORK_URL,
        channelName: process.env.CHANNEL_NAME,
        chaincodeName: process.env.CHAINCODE_NAME
    }
};
