#!/bin/bash

# D<PERSON>marrer le backend
echo "Starting backend server..."
cd backend && npm run dev &
BACKEND_PID=$!

# Attendre que le backend démarre
sleep 5

# Démarrer le frontend
echo "Starting frontend application..."
cd ../frontend && npm start &
FRONTEND_PID=$!

# Fonction pour arrêter proprement les processus
cleanup() {
    echo "Stopping services..."
    kill $FRONTEND_PID
    kill $BACKEND_PID
    exit
}

# Capturer SIGINT (Ctrl+C)
trap cleanup SIGINT

# Garder le script en cours d'exécution
wait