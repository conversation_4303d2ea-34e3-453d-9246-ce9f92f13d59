const express = require('express');
const cors = require('cors');
const morgan = require('morgan');

const app = express();

// Logging middleware
app.use(morgan('dev'));

// CORS configuration
app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    exposedHeaders: ['Content-Length', 'X-Requested-With']
}));

// Body parser
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check route
app.get('/api/v1/health', (req, res) => {
    res.json({ status: 'ok', message: 'API is running' });
});

// Routes
app.use('/api/v1/auth', require('./api/routes/auth.routes'));

// Error handling
app.use((err, req, res, next) => {
    console.error('Error:', {
        method: req.method,
        url: req.originalUrl,
        body: req.body,
        error: err.message,
        stack: err.stack
    });
    
    res.status(err.status || 500).json({
        status: 'error',
        message: err.message || 'Une erreur est survenue'
    });
});

module.exports = app;
