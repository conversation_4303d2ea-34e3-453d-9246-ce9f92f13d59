
#!/bin/bash

# D<PERSON>marrer le backend
echo "Démarrage du serveur backend..."
cd backend && npm run dev &
BACKEND_PID=$!

# Attendre que le backend soit prêt
sleep 5

# Démarrer le frontend
echo "Démarrage de l'application frontend..."
cd .. && npm start &
FRONTEND_PID=$!

# Fonction pour arrêter proprement les processus
function cleanup {
  echo "Arrêt des services..."
  kill $FRONTEND_PID
  kill $BACKEND_PID
  exit
}

# Capturer SIGINT (Ctrl+C)
trap cleanup SIGINT

# Garder le script en cours d'exécution
wait
