'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 1. D'abord, ajoutez la colonne sans contrainte unique
    await queryInterface.addColumn('Users', 'username', {
      type: Sequelize.STRING,
      allowNull: true // temporairement permettre null
    });

    // 2. Met<PERSON>z à jour les enregistrements existants avec un username par défaut
    await queryInterface.sequelize.query(`
      UPDATE Users 
      SET username = CONCAT('user_', id) 
      WHERE username IS NULL OR username = '';
    `);

    // 3. Ajoutez la contrainte unique et NOT NULL
    await queryInterface.changeColumn('Users', 'username', {
      type: Sequelize.STRING,
      allowNull: false,
      unique: true
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Users', 'username');
  }
};