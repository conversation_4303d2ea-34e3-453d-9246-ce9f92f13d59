#!/bin/bash

# C<PERSON>er le dossier public s'il n'existe pas
mkdir -p public

# Créer le fichier index.html
cat > public/index.html << 'EOL'
<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="EduBlockchain - Application de gestion éducative basée sur la blockchain"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>EduBlockchain</title>
  </head>
  <body>
    <noscript>V<PERSON> devez activer JavaScript pour exécuter cette application.</noscript>
    <div id="root"></div>
  </body>
</html>
EOL

# C<PERSON>er le fichier manifest.json
cat > public/manifest.json << 'EOL'
{
  "short_name": "EduBlockchain",
  "name": "EduBlockchain - Application de gestion éducative",
  "icons": [
    {
      "src": "favicon.ico",
      "sizes": "64x64 32x32 24x24 16x16",
      "type": "image/x-icon"
    },
    {
      "src": "logo192.png",
      "type": "image/png",
      "sizes": "192x192"
    },
    {
      "src": "logo512.png",
      "type": "image/png",
      "sizes": "512x512"
    }
  ],
  "start_url": ".",
  "display": "standalone",
  "theme_color": "#000000",
  "background_color": "#ffffff"
}
EOL

# Créer un favicon de base (optionnel)
# Vous pouvez remplacer cela par votre propre favicon
echo "Vous devrez ajouter votre propre favicon.ico, logo192.png et logo512.png dans le dossier public"

echo "Structure du dossier public créée avec succès!"