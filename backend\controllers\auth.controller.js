const AuthService = require('../services/auth.service');
const jwt = require('jsonwebtoken');
const { generateOTP, sendOTP } = require('../utils/otp');

const authController = {
    async initiateLogin(req, res) {
        try {
            const { did, password } = req.body;
            console.log('Tentative de connexion reçue pour:', did);

            const user = await AuthService.validateCredentials(did, password);
            
            if (!user) {
                console.log('Authentification échouée pour:', did);
                return res.status(401).json({
                    status: 'error',
                    message: 'Identifiants invalides'
                });
            }

            console.log('Utilisateur authentifié:', user);

            // Pour l'admin, générer un token JWT
            if (did === 'did:edu:admin') {
                const token = jwt.sign(
                    { 
                        did: user.did,
                        role: user.role 
                    },
                    process.env.JWT_SECRET || 'votre-secret-temporaire',
                    { expiresIn: '1h' }
                );

                return res.status(200).json({
                    status: 'success',
                    data: {
                        token,
                        user: {
                            did: user.did,
                            role: user.role,
                            email: user.email
                        }
                    }
                });
            }

            // Pour les autres utilisateurs, générer et envoyer l'OTP
            const otp = generateOTP();
            await AuthService.storeOTP(did, otp);
            await sendOTP(user.email, otp);

            res.status(200).json({
                status: 'success',
                message: 'Code OTP envoyé avec succès',
                data: {
                    requireOTP: true,
                    user: {
                        did: user.did,
                        email: user.email
                    }
                }
            });
        } catch (error) {
            console.error('Erreur d\'authentification:', error);
            res.status(500).json({
                status: 'error',
                message: 'Erreur lors de l\'authentification'
            });
        }
    }
};

module.exports = authController;
