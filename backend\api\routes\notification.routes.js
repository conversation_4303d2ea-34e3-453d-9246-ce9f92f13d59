const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notification.controller');
const { authMiddleware } = require('../../middleware/auth.middleware');

// Routes protégées
router.use(authMiddleware);

// Créer une notification
router.post('/', notificationController.createNotification);

// Récupérer les notifications non lues
router.get('/unread/:userId', notificationController.getUnreadNotifications);

// Marquer une notification comme lue
router.put('/:id/read', notificationController.markAsRead);

module.exports = router;
