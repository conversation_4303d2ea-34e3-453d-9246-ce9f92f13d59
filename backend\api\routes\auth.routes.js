const express = require('express');
const router = express.Router();
const authController = require('../controllers/auth.controller');

// Middleware de logging pour le débogage
router.use((req, res, next) => {
    console.log(`Auth Route: ${req.method} ${req.url}`);
    next();
});

// Routes d'authentification
router.post('/register', authController.register);
router.post('/login', authController.login);

module.exports = router;
