{"name": "edublock", "version": "1.0.0", "type": "module", "scripts": {"start": "node --experimental-json-modules scripts/start.js", "build": "node --experimental-json-modules scripts/build.js", "test": "node --experimental-json-modules scripts/test.js", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:drop && sequelize-cli db:create && sequelize-cli db:migrate && sequelize-cli db:seed:all"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@reduxjs/toolkit": "^2.6.0", "@types/web3": "^1.0.20", "axios": "^1.6.7", "bcryptjs": "^3.0.2", "dotenv": "^16.4.7", "formik": "^2.4.6", "jsonwebtoken": "^9.0.2", "mysql2": "^3.12.0", "nodemailer": "^6.10.0", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.15.1", "react-router-dom": "^6.22.0", "react-tsparticles": "^2.12.2", "redis": "^4.0.0", "sequelize": "^6.37.5", "socket.io-client": "^4.7.2", "tsparticles": "^2.12.0", "uuid": "^11.1.0", "web3": "^1.10.0", "ws": "^8.18.1", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "babel-loader": "^9.1.3", "bcrypt": "^5.1.1", "chalk": "^5.3.0", "css-loader": "^6.5.1", "html-webpack-plugin": "^5.5.0", "sequelize-cli": "^6.6.2", "style-loader": "^3.3.1", "typescript": "^4.9.5", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}}