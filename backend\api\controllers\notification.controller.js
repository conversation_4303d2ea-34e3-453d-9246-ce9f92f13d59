const notificationService = require('../../services/notification.service');
const logger = require('../../utils/logger');

class NotificationController {
    async getUnreadNotifications(req, res, next) {
        try {
            const { userId } = req.params;
            const notifications = await notificationService.getUnreadNotifications(userId);
            res.json({
                status: 'success',
                data: notifications
            });
        } catch (error) {
            logger.error('Failed to fetch notifications:', error);
            next(error);
        }
    }

    async markAsRead(req, res, next) {
        try {
            const { id } = req.params;
            await notificationService.markAsRead(id);
            res.json({
                status: 'success',
                message: 'Notification marked as read'
            });
        } catch (error) {
            logger.error('Failed to mark notification as read:', error);
            next(error);
        }
    }

    async createNotification(req, res, next) {
        try {
            const notification = await notificationService.createNotification({
                ...req.body,
                createdAt: new Date().toISOString(),
                read: false
            });

            // Émettre la notification via WebSocket si nécessaire
            if (req.app.get('io')) {
                req.app.get('io').to(notification.userId).emit('notification', notification);
            }

            res.status(201).json({
                status: 'success',
                data: notification
            });
        } catch (error) {
            console.error('Failed to create notification:', error);
            next(error);
        }
    }
}

module.exports = new NotificationController();
