module.exports = (sequelize, DataTypes) => {
    const User = sequelize.define('User', {
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            primaryKey: true
        },
        firstName: {
            type: DataTypes.STRING,
            allowNull: false
        },
        lastName: {
            type: DataTypes.STRING,
            allowNull: false
        },
        email: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                isEmail: true
            }
        },
        password: {
            type: DataTypes.STRING,
            allowNull: false
        },
        role: {
            type: DataTypes.ENUM('STUDENT', 'TEACHER'),
            allowNull: false
        },
        specialization: {
            type: DataTypes.STRING,
            allowNull: true
        },
        studentId: {
            type: DataTypes.STRING,
            allowNull: true
        },
        did: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true
        },
        status: {
            type: DataTypes.ENUM('PENDING', 'ACTIVE', 'SUSPENDED'),
            defaultValue: 'PENDING'
        }
    });

    return User;
};
