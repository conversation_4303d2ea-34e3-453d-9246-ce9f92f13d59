#!/bin/bash

# create-project-structure.sh

# Supprimer le contenu existant de src
rm -rf src/*

# Créer la structure des dossiers
mkdir -p src/{assets/{images,styles},components/{common/{Button,Loading,ErrorBoundary},courses/{CourseCard,CourseList,CourseForm},grades/{GradeTable,GradeInput},documents/{DocumentUpload,DocumentList}},config,contracts/{artifacts,typechain,interfaces},hooks,layouts/{MainLayout,AuthLayout},pages/{auth,courses,grades,documents},services/{api,blockchain},store/slices,types,utils}

# Créer les fichiers de base
touch src/assets/styles/theme.ts

# Components
echo "export * from './Button';" > src/components/common/Button/index.ts
echo "import React from 'react';
import { Button as MuiButton } from '@mui/material';
export const Button: React.FC = () => <MuiButton>Button</MuiButton>;" > src/components/common/Button/Button.tsx
echo "import { render } from '@testing-library/react';
import { Button } from './Button';" > src/components/common/Button/Button.test.tsx

# Config
echo "export const CONFIG = {
  APP_NAME: 'EduBlockchain',
  API_URL: process.env.REACT_APP_API_URL,
} as const;" > src/config/constants.ts

echo "export const ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
  },
} as const;" > src/config/endpoints.ts

# Hooks
echo "import { useState, useEffect } from 'react';
export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  return { isAuthenticated };
};" > src/hooks/useAuth.ts

echo "export * from './useAuth';" > src/hooks/index.ts

# Pages
echo "import React from 'react';
export const Login: React.FC = () => <div>Login</div>;" > src/pages/auth/Login.tsx

echo "import React from 'react';
export const Register: React.FC = () => <div>Register</div>;" > src/pages/auth/Register.tsx

# Services
echo "import axios from 'axios';
export const authApi = {
  login: async (credentials: any) => {
    return axios.post('/auth/login', credentials);
  },
};" > src/services/api/auth.api.ts

echo "import Web3 from 'web3';
export const getWeb3 = async () => {
  if (window.ethereum) {
    return new Web3(window.ethereum);
  }
  throw new Error('No Web3 Provider found');
};" > src/services/blockchain/web3.ts

# Store
echo "import { createSlice } from '@reduxjs/toolkit';
export const authSlice = createSlice({
  name: 'auth',
  initialState: { user: null },
  reducers: {},
});" > src/store/slices/authSlice.ts

echo "import { configureStore } from '@reduxjs/toolkit';
import { authSlice } from './slices/authSlice';
export const store = configureStore({
  reducer: {
    auth: authSlice.reducer,
  },
});" > src/store/store.ts

# Types
echo "export interface User {
  id: string;
  email: string;
  role: string;
}" > src/types/auth.types.ts

echo "export interface Course {
  id: string;
  title: string;
  description: string;
}" > src/types/course.types.ts

echo "export interface Grade {
  id: string;
  value: number;
  courseId: string;
}" > src/types/grade.types.ts

# Utils
echo "export const formatDate = (date: Date) => {
  return date.toLocaleDateString();
};" > src/utils/formatters.ts

echo "export const validateEmail = (email: string) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};" > src/utils/validators.ts

# Root files
echo "import React from 'react';
import { Provider } from 'react-redux';
import { store } from './store/store';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <div>Education Blockchain App</div>
    </Provider>
  );
};

export default App;" > src/App.tsx

echo "import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);" > src/index.tsx

# Package.json update
npm install @mui/material @emotion/react @emotion/styled @reduxjs/toolkit react-redux axios web3 react-router-dom
npm install --save-dev typescript @types/react @types/react-dom @types/node @testing-library/react @testing-library/jest-dom

# TypeScript config
echo '{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "src"
  },
  "include": ["src"],
  "exclude": ["node_modules"]
}' > tsconfig.json

# Env file
echo 'REACT_APP_API_URL=http://localhost:3001' > .env

echo "Project structure created successfully!"
